import { readFileSync } from 'fs'
import { join } from 'path'

/**
 * Configuration for the main process
 * Reads from .env file in the project root
 */

// Default values as fallbacks
const DEFAULT_BOT_TARGET_URL = 'http://localhost:5174'

/**
 * Parse a simple .env file
 * This is a lightweight implementation since we don't want to add dotenv as a dependency
 */
const parseEnvFile = (filePath: string): Record<string, string> => {
  try {
    const content = readFileSync(filePath, 'utf-8')
    const env: Record<string, string> = {}
    
    content.split('\n').forEach(line => {
      // Skip empty lines and comments
      const trimmedLine = line.trim()
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        return
      }
      
      // Parse KEY=VALUE format
      const equalIndex = trimmedLine.indexOf('=')
      if (equalIndex > 0) {
        const key = trimmedLine.substring(0, equalIndex).trim()
        const value = trimmedLine.substring(equalIndex + 1).trim()
        
        // Remove quotes if present
        const cleanValue = value.replace(/^["']|["']$/g, '')
        env[key] = cleanValue
      }
    })
    
    return env
  } catch (error) {
    console.log('No .env file found or error reading it, using defaults')
    return {}
  }
}

/**
 * Load environment variables from .env file
 */
const loadEnvConfig = (): Record<string, string> => {
  // Try to find .env file in the project root
  // In development, __dirname will be in out/main, so we go up two levels
  // In production, we need to account for the app.asar structure
  const possiblePaths = [
    join(__dirname, '../../.env'),           // Development
    join(__dirname, '../../../.env'),        // Alternative development path
    join(process.cwd(), '.env'),             // Current working directory
    join(process.resourcesPath, '../../.env') // Production (if .env is included)
  ]
  
  for (const envPath of possiblePaths) {
    try {
      const env = parseEnvFile(envPath)
      if (Object.keys(env).length > 0) {
        console.log(`Loaded environment config from: ${envPath}`)
        return env
      }
    } catch (error) {
      // Continue to next path
    }
  }
  
  console.log('No .env file found, using default configuration')
  return {}
}

// Load the environment configuration
const envConfig = loadEnvConfig()

/**
 * Get the bot target URL from environment variables
 * Falls back to default if not set
 */
export const getBotTargetUrl = (): string => {
  return envConfig.VITE_BOT_TARGET_URL || process.env.VITE_BOT_TARGET_URL || DEFAULT_BOT_TARGET_URL
}

/**
 * Configuration object with all environment-based settings
 */
export const config = {
  botTargetUrl: getBotTargetUrl()
} as const

// Log the configuration on startup
console.log('Main process configuration:', {
  botTargetUrl: config.botTargetUrl
})
