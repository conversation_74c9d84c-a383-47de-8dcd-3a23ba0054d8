interface TradingAnalysisResponse {
  assetName: string
  expiration: string
  description: string
  confidence?: number
  error?: string
  tradeSignal?: 'buy' | 'sell' | 'hold'
}

/**
 *
 * @param chartImage - File, Blob, or Base64 string of the chart image.
 * @param assetName
 * @param selectedTimeframe
 */
export async function AnalyzeTradingChart(
  imageInput: string | File | Blob,
  assetName: string,
  selectedTimeframe: string
): Promise<TradingAnalysisResponse> {
  if (!process.env.OPENROUTER_API_KEY) {
    throw new Error('OpenRouter API key is not defined')
  }

  try {
    let dataUrl: string
    if (typeof imageInput === 'string') {
      if (imageInput.startsWith('data:')) {
        dataUrl = imageInput
      } else {
        dataUrl = `data:image/jpeg;base64,${imageInput}`
      }
    } else {
      dataUrl = await fileToDataUrl(imageInput)
    }
    const result = await fetch(`https://openrouter.ai/api/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`
      },
      body: JSON.stringify({
        model: process.env.OPENROUTER_MODEL,
        messages: [
          {
            role: 'system',
            content: `You are an AI Expert Trading Analyst. Your task is to analyze the provided trading chart image and provide trading recommendations for ${assetName}.
						### Your analysis should include the following:
						1. Identify the current price trend (upward, downward, or sideways).
						2. Determine the support and resistance levels.
						3. Evaluate the volume and its relationship with price movements.
						4. Assess the technical indicators (e.g., moving averages, RSI, MACD) and their alignment with the price trend.
						5. Provide a confidence score for your analysis (0-100). (the higher the level, the more successful the trade will win)
						6. Suggest potential entry and exit points based on your analysis.
						7. Offer a risk management strategy, including stop-loss and take-profit levels.
						8. Provide a trading plan, including the timeframe (${selectedTimeframe}) based on the recommended trading strategy.
						
						Your response should be in JSON format with the following keys:
						- assetName: The name of the asset being analyzed.
						- expiration: ${selectedTimeframe}
						- confidence: A confidence score for your analysis (0-100) in percentage.
						- tradeSignal: (buy, sell, hold) strictly based on the analysis.
						- description: A detailed analysis of the trading chart and your recommendations.
						- error: Any errors or issues encountered during the analysis **if there are any**.
						`
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: 'Analyze the provided trading chart image and provide trading recommendations.'
              },
              {
                type: 'image_url',
                image_url: {
                  url: dataUrl
                }
              }
            ]
          }
        ]
      })
    })

    if (!result.ok) {
      const errorData = await result.json().catch(() => ({}))
      throw new Error(
        `OpenRouter API error: ${result.status} - ${errorData.error?.message || result.statusText}`
      )
    }

    const data = await result.json()
    if (!data.choices || data.choices.length === 0) {
      throw new Error('No response received from the model')
    }

    try {
      const content = data.choices[0].message.content
      return JSON.parse(content) as TradingAnalysisResponse
    } catch (parseError) {
      throw new Error(`Failed to parse AI response: ${parseError}`)
    }
  } catch (e) {
    console.error('Error analyzing trading chart:', e)
    throw e
  }
}

async function fileToDataUrl(file: File | Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}
